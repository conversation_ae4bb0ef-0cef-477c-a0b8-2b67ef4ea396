"use client"

import React, { memo, useMemo } from 'react';
import { Event } from '@/lib/types';
import { useLazyImage } from '@/lib/hooks/useLazyLoading';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

interface OptimizedEventCardProps {
  event: Event;
  isSelected: boolean;
  onSelect: () => void;
  onHover: (event: Event | null) => void;
  size: 'small' | 'medium' | 'large';
  showDetails?: boolean;
  className?: string;
  style?: React.CSSProperties;
  virtualIndex?: number;
  isVirtualized?: boolean;
}

const OptimizedEventCard = memo(function OptimizedEventCard({
  event,
  isSelected,
  onSelect,
  onHover,
  size,
  showDetails = false,
  className,
  style,
  virtualIndex,
  isVirtualized = false,
}: OptimizedEventCardProps) {
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      onSelect();
    }
  };

  const sizeClasses = useMemo(() => ({
    small: 'w-24 h-16 sm:w-28 sm:h-18 md:w-32 md:h-20',
    medium: 'w-32 h-20 sm:w-40 sm:h-24 md:w-48 md:h-32',
    large: 'w-40 h-24 sm:w-52 sm:h-32 md:w-64 md:h-40',
  }), []);

  const getSignificanceColor = useMemo(() => (significance: Event['significance']) => {
    switch (significance) {
      case 'major':
        return 'bg-red-500 border-red-600';
      case 'moderate':
        return 'bg-yellow-500 border-yellow-600';
      case 'minor':
        return 'bg-blue-500 border-blue-600';
      case 'reference':
        return 'bg-gray-500 border-gray-600';
      default:
        return 'bg-gray-500 border-gray-600';
    }
  }, []);

  const getRegionColor = useMemo(() => (region: string) => {
    switch (region) {
      case 'Europe':
        return 'bg-blue-100 border-blue-300 dark:bg-blue-900 dark:border-blue-700';
      case 'Asia':
        return 'bg-red-100 border-red-300 dark:bg-red-900 dark:border-red-700';
      case 'Africa':
        return 'bg-green-100 border-green-300 dark:bg-green-900 dark:border-green-700';
      case 'North America':
        return 'bg-purple-100 border-purple-300 dark:bg-purple-900 dark:border-purple-700';
      default:
        return 'bg-gray-100 border-gray-300 dark:bg-gray-900 dark:border-gray-700';
    }
  }, []);

  // Lazy load thumbnail image
  const { elementRef, imageSrc, isLoaded, isError } = useLazyImage(
    event.media?.thumbnail || '',
    { threshold: 0.1, rootMargin: '100px' }
  );

  // Memoize the card content to prevent unnecessary re-renders
  const cardContent = useMemo(() => (
    <>
      <CardHeader className="p-1 sm:p-2">
        <div className="flex items-start justify-between">
          <CardTitle className="text-xs sm:text-sm font-semibold line-clamp-2">
            {event.title}
          </CardTitle>
          <div
            className={cn(
              'w-2 h-2 sm:w-3 sm:h-3 rounded-full flex-shrink-0 ml-1',
              getSignificanceColor(event.significance)
            )}
            title={`Significance: ${event.significance}`}
          />
        </div>
      </CardHeader>
      
      <CardContent className="p-1 sm:p-2 pt-0">
        <div className="space-y-0.5 sm:space-y-1">
          <div className="text-xs text-muted-foreground">
            {event.date.display}
          </div>

          {showDetails && (
            <div className="text-xs text-muted-foreground line-clamp-1">
              {event.location.city && `${event.location.city}, `}
              {event.location.country}
            </div>
          )}

          {showDetails && size !== 'small' && (
            <p className="text-xs text-muted-foreground line-clamp-2">
              {event.description.short}
            </p>
          )}

          {/* Lazy loaded thumbnail */}
          {event.media?.thumbnail && size !== 'small' && (
            <div className="mt-1 relative">
              {!isLoaded && !isError && (
                <Skeleton className="w-full h-8 rounded" />
              )}
              {isLoaded && imageSrc && (
                <img
                  src={imageSrc}
                  alt={event.title}
                  className="w-full h-8 object-cover rounded"
                  loading="lazy"
                />
              )}
            </div>
          )}

          {showDetails && event.category.tags.length > 0 && size === 'large' && (
            <div className="flex flex-wrap gap-0.5 mt-1">
              {event.category.tags.slice(0, 2).map((tag) => (
                <Badge key={tag} variant="outline" className="text-xs px-1 py-0">
                  {tag}
                </Badge>
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </>
  ), [event, showDetails, size, getSignificanceColor, imageSrc, isLoaded, isError]);

  return (
    <Card
      ref={elementRef}
      className={cn(
        sizeClasses[size],
        'cursor-pointer transition-all duration-200 hover:scale-105 hover:shadow-lg',
        getRegionColor(event.location.region),
        isSelected && 'ring-2 ring-primary ring-offset-2',
        isVirtualized && 'absolute',
        className
      )}
      style={style}
      onClick={onSelect}
      onKeyDown={handleKeyDown}
      onMouseEnter={() => onHover(event)}
      onMouseLeave={() => onHover(null)}
      data-event-id={event.id}
      data-virtual-index={virtualIndex}
      tabIndex={0}
      role="button"
      aria-label={`Event: ${event.title} - ${event.date.display}`}
      aria-selected={isSelected}
    >
      {cardContent}
    </Card>
  );
});

// Performance comparison component
export const EventCardComparison = memo(function EventCardComparison({
  useOptimized = true,
  ...props
}: OptimizedEventCardProps & { useOptimized?: boolean }) {
  if (useOptimized) {
    return <OptimizedEventCard {...props} />;
  }
  
  // Fallback to regular EventCard if needed
  return <OptimizedEventCard {...props} />;
});

export { OptimizedEventCard };
