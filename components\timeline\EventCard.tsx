"use client"

import React from 'react';
import { Event } from '@/lib/types';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface EventCardProps {
  event: Event;
  isSelected: boolean;
  onSelect: () => void;
  onHover: (event: Event | null) => void;
  size: 'small' | 'medium' | 'large';
  showDetails?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

export function EventCard({
  event,
  isSelected,
  onSelect,
  onHover,
  size,
  showDetails = false,
  className,
  style,
}: EventCardProps) {
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      onSelect();
    }
  };
  const sizeClasses = {
    small: 'w-24 h-16 sm:w-28 sm:h-18 md:w-32 md:h-20',
    medium: 'w-32 h-20 sm:w-40 sm:h-24 md:w-48 md:h-32',
    large: 'w-40 h-24 sm:w-52 sm:h-32 md:w-64 md:h-40',
  };

  const getSignificanceColor = (significance: Event['significance']) => {
    switch (significance) {
      case 'major':
        return 'bg-red-500 border-red-600';
      case 'moderate':
        return 'bg-yellow-500 border-yellow-600';
      case 'minor':
        return 'bg-blue-500 border-blue-600';
      case 'reference':
        return 'bg-gray-500 border-gray-600';
      default:
        return 'bg-gray-500 border-gray-600';
    }
  };

  const getRegionColor = (region: string) => {
    switch (region) {
      case 'Europe':
        return 'bg-blue-100 border-blue-300 dark:bg-blue-900 dark:border-blue-700';
      case 'Asia':
        return 'bg-red-100 border-red-300 dark:bg-red-900 dark:border-red-700';
      case 'Africa':
        return 'bg-green-100 border-green-300 dark:bg-green-900 dark:border-green-700';
      case 'North America':
        return 'bg-purple-100 border-purple-300 dark:bg-purple-900 dark:border-purple-700';
      default:
        return 'bg-gray-100 border-gray-300 dark:bg-gray-900 dark:border-gray-700';
    }
  };

  return (
    <Card
      className={cn(
        sizeClasses[size],
        'cursor-pointer transition-all duration-200 hover:scale-105 hover:shadow-lg',
        getRegionColor(event.location.region),
        isSelected && 'ring-2 ring-primary ring-offset-2',
        className
      )}
      style={style}
      onClick={onSelect}
      onKeyDown={handleKeyDown}
      onMouseEnter={() => onHover(event)}
      onMouseLeave={() => onHover(null)}
      data-event-id={event.id}
      tabIndex={0}
      role="button"
      aria-label={`Event: ${event.title} - ${event.date.display}`}
      aria-selected={isSelected}
    >
      <CardHeader className="p-1 sm:p-2">
        <div className="flex items-start justify-between">
          <CardTitle className="text-xs sm:text-sm font-semibold line-clamp-2">
            {event.title}
          </CardTitle>
          <div
            className={cn(
              'w-2 h-2 sm:w-3 sm:h-3 rounded-full flex-shrink-0 ml-1',
              getSignificanceColor(event.significance)
            )}
            title={`Significance: ${event.significance}`}
          />
        </div>
      </CardHeader>
      
      <CardContent className="p-1 sm:p-2 pt-0">
        <div className="space-y-0.5 sm:space-y-1">
          <div className="text-xs text-muted-foreground">
            {event.date.display}
          </div>

          <div className="text-xs text-muted-foreground truncate">
            {event.location.city ? `${event.location.city}, ` : ''}
            {event.location.country}
          </div>

          {size !== 'small' && (
            <p className="text-xs text-muted-foreground line-clamp-2">
              {event.description.short}
            </p>
          )}

          {size === 'large' && showDetails && (
            <div className="space-y-0.5 sm:space-y-1">
              <Badge variant="secondary" className="text-xs">
                {event.category.primary}
              </Badge>

              {event.category.tags.slice(0, 2).map((tag) => (
                <Badge key={tag} variant="outline" className="text-xs mr-1">
                  {tag}
                </Badge>
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
