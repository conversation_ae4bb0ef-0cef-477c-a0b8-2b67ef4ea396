"use client"

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { X, Keyboard, Navigation, Zap, Search, Map } from 'lucide-react';

interface KeyboardShortcutsHelpProps {
  isOpen: boolean;
  onClose: () => void;
  className?: string;
}

interface ShortcutGroup {
  title: string;
  icon: React.ReactNode;
  shortcuts: Array<{
    keys: string[];
    description: string;
  }>;
}

export function KeyboardShortcutsHelp({ isOpen, onClose, className }: KeyboardShortcutsHelpProps) {
  const shortcutGroups: ShortcutGroup[] = [
    {
      title: "Timeline Navigation",
      icon: <Navigation className="h-4 w-4" />,
      shortcuts: [
        { keys: ["←", "→"], description: "Pan timeline left/right" },
        { keys: ["↑", "↓"], description: "Navigate between events" },
        { keys: ["Home"], description: "Go to first event" },
        { keys: ["End"], description: "Go to last event" },
        { keys: ["Page Up"], description: "Jump 5 events back" },
        { keys: ["Page Down"], description: "Jump 5 events forward" },
      ]
    },
    {
      title: "Event Actions",
      icon: <Zap className="h-4 w-4" />,
      shortcuts: [
        { keys: ["Enter", "Space"], description: "Open event details" },
        { keys: ["Escape"], description: "Close modal/dialog" },
        { keys: ["R"], description: "Go to random event" },
        { keys: ["1", "2", "3", "4", "5"], description: "Jump to event by number" },
      ]
    },
    {
      title: "Zoom Controls",
      icon: <Search className="h-4 w-4" />,
      shortcuts: [
        { keys: ["+", "="], description: "Zoom in" },
        { keys: ["-"], description: "Zoom out" },
        { keys: ["0"], description: "Zoom to fit all events" },
      ]
    },
    {
      title: "Quick Actions",
      icon: <Map className="h-4 w-4" />,
      shortcuts: [
        { keys: ["F"], description: "Focus search bar" },
        { keys: ["M"], description: "Toggle map view" },
        { keys: ["H", "?"], description: "Show this help dialog" },
      ]
    }
  ];

  const renderKey = (key: string) => (
    <Badge 
      key={key} 
      variant="outline" 
      className="px-2 py-1 text-xs font-mono bg-muted/50 border-muted-foreground/20"
    >
      {key}
    </Badge>
  );

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          transition={{ duration: 0.2 }}
          onClick={(e) => e.stopPropagation()}
          className={cn("w-full max-w-4xl max-h-[90vh] overflow-y-auto", className)}
        >
          <Card className="shadow-2xl border-2">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Keyboard className="h-5 w-5 text-primary" />
                  <CardTitle className="text-xl">Keyboard Shortcuts</CardTitle>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={onClose}
                  className="h-8 w-8"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              <p className="text-sm text-muted-foreground">
                Use these keyboard shortcuts to navigate the timeline efficiently
              </p>
            </CardHeader>

            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {shortcutGroups.map((group) => (
                  <div key={group.title} className="space-y-3">
                    <div className="flex items-center gap-2">
                      {group.icon}
                      <h3 className="font-semibold text-sm">{group.title}</h3>
                    </div>
                    
                    <div className="space-y-2">
                      {group.shortcuts.map((shortcut, index) => (
                        <div 
                          key={index}
                          className="flex items-center justify-between gap-3 p-2 rounded-md bg-muted/30 hover:bg-muted/50 transition-colors"
                        >
                          <span className="text-sm text-muted-foreground flex-1">
                            {shortcut.description}
                          </span>
                          <div className="flex items-center gap-1">
                            {shortcut.keys.map((key, keyIndex) => (
                              <React.Fragment key={keyIndex}>
                                {keyIndex > 0 && (
                                  <span className="text-xs text-muted-foreground mx-1">or</span>
                                )}
                                {renderKey(key)}
                              </React.Fragment>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>

              {/* Tips Section */}
              <div className="mt-6 p-4 bg-primary/5 rounded-lg border border-primary/20">
                <h4 className="font-semibold text-sm mb-2 flex items-center gap-2">
                  <Zap className="h-4 w-4 text-primary" />
                  Pro Tips
                </h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Use arrow keys to quickly navigate through events</li>
                  <li>• Press numbers 1-5 to jump to the first 5 visible events</li>
                  <li>• Use Page Up/Down to jump multiple events at once</li>
                  <li>• Press 'F' to quickly search for specific events</li>
                  <li>• Keyboard shortcuts work throughout the application</li>
                </ul>
              </div>

              {/* Footer */}
              <div className="flex items-center justify-between pt-4 border-t">
                <div className="text-xs text-muted-foreground">
                  Press <Badge variant="outline" className="px-1 py-0 text-xs">Esc</Badge> or{' '}
                  <Badge variant="outline" className="px-1 py-0 text-xs">H</Badge> to close
                </div>
                <Button onClick={onClose} size="sm">
                  Got it!
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}
