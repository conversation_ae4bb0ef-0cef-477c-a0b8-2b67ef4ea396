"use client"

import React from 'react';
import { Event } from '@/lib/types';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Sheet, SheetContent, SheetHeader, SheetTitle } from '@/components/ui/sheet';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { X, MapPin, Calendar, Users, ExternalLink, ChevronLeft, ChevronRight } from 'lucide-react';
import { EventCarousel } from './EventCarousel';
import { RelatedEvents } from './RelatedEvents';
import { EventNavigation } from './EventNavigation';
import { SourceDisplay } from './SourceDisplay';
import { cn } from '@/lib/utils';

interface EventDetailsProps {
  event: Event | null;
  isOpen: boolean;
  onClose: () => void;
  onNavigate?: (event: Event) => void;
  allEvents?: Event[];
  variant?: 'modal' | 'sidebar';
  className?: string;
}

export function EventDetails({
  event,
  isOpen,
  onClose,
  onNavigate,
  allEvents = [],
  variant = 'modal',
  className,
}: EventDetailsProps) {
  if (!event) return null;

  const formatDate = (dateStr: string) => {
    const year = parseInt(dateStr.split('-')[0]);
    if (year < 0) {
      return `${Math.abs(year)} BCE`;
    } else if (year === 0) {
      return '1 CE';
    } else {
      return `${year} CE`;
    }
  };

  const getSignificanceColor = (significance: Event['significance']) => {
    switch (significance) {
      case 'major':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'moderate':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'minor':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'reference':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const EventContent = () => (
    <ScrollArea className="h-full">
      <div className="space-y-6 p-1">
        {/* Header Section */}
        <div className="space-y-4">
          <div className="flex items-start justify-between">
            <div className="space-y-2 flex-1">
              <h2 className="text-2xl font-bold leading-tight">{event.title}</h2>
              <div className="flex flex-wrap items-center gap-2">
                <Badge className={getSignificanceColor(event.significance)}>
                  {event.significance}
                </Badge>
                <Badge variant="outline">{event.eventType}</Badge>
                <Badge variant="secondary">{event.category.primary}</Badge>
              </div>
            </div>
          </div>

          {/* Date and Location */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="font-medium">{event.date.display}</p>
                <p className="text-sm text-muted-foreground">
                  {formatDate(event.date.start)}
                  {event.date.end && ` - ${formatDate(event.date.end)}`}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="font-medium">
                  {event.location.city && `${event.location.city}, `}
                  {event.location.country}
                </p>
                <p className="text-sm text-muted-foreground">
                  {event.location.region}
                  {event.location.subregion && `, ${event.location.subregion}`}
                </p>
              </div>
            </div>
          </div>
        </div>

        <Separator />

        {/* Images */}
        {event.media?.images && event.media.images.length > 0 && (
          <div className="space-y-3">
            <h3 className="text-lg font-semibold">Images</h3>
            <EventCarousel event={event} showThumbnails={true} />
          </div>
        )}

        {event.media?.images && event.media.images.length > 0 && <Separator />}

        {/* Description */}
        <div className="space-y-3">
          <h3 className="text-lg font-semibold">Description</h3>
          <p className="text-muted-foreground leading-relaxed">
            {event.description.long}
          </p>
        </div>

        {/* Key Figures */}
        {event.figures && event.figures.length > 0 && (
          <>
            <Separator />
            <div className="space-y-3">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Users className="h-4 w-4" />
                Key Figures
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {event.figures.map((figure) => (
                  <div key={figure.id} className="p-3 border rounded-lg">
                    <p className="font-medium">{figure.name}</p>
                    <p className="text-sm text-muted-foreground">{figure.role}</p>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}

        {/* Categories and Tags */}
        <Separator />
        <div className="space-y-3">
          <h3 className="text-lg font-semibold">Categories & Tags</h3>
          <div className="space-y-2">
            <div>
              <p className="text-sm font-medium mb-1">Primary Category:</p>
              <Badge variant="default">{event.category.primary}</Badge>
            </div>
            
            {event.category.secondary && event.category.secondary.length > 0 && (
              <div>
                <p className="text-sm font-medium mb-1">Secondary Categories:</p>
                <div className="flex flex-wrap gap-1">
                  {event.category.secondary.map((cat) => (
                    <Badge key={cat} variant="secondary">{cat}</Badge>
                  ))}
                </div>
              </div>
            )}
            
            <div>
              <p className="text-sm font-medium mb-1">Tags:</p>
              <div className="flex flex-wrap gap-1">
                {event.category.tags.map((tag) => (
                  <Badge key={tag} variant="outline">{tag}</Badge>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Sources */}
        <Separator />
        <div className="space-y-3">
          <h3 className="text-lg font-semibold">Sources & References</h3>
          <SourceDisplay
            event={event}
            showReliabilityInfo={true}
            maxSources={5}
          />
        </div>

        {/* Event Navigation */}
        {onNavigate && allEvents.length > 0 && (
          <>
            <Separator />
            <div className="space-y-3">
              <h3 className="text-lg font-semibold">Navigation</h3>
              <EventNavigation
                currentEvent={event}
                allEvents={allEvents}
                onEventSelect={onNavigate}
              />
            </div>
          </>
        )}

        {/* Related Events */}
        {onNavigate && allEvents.length > 0 && (
          <>
            <Separator />
            <div className="space-y-3">
              <h3 className="text-lg font-semibold">Related Events</h3>
              <RelatedEvents
                currentEvent={event}
                allEvents={allEvents}
                onEventSelect={onNavigate}
                maxRelated={3}
              />
            </div>
          </>
        )}
      </div>
    </ScrollArea>
  );

  if (variant === 'sidebar') {
    return (
      <Sheet open={isOpen} onOpenChange={onClose}>
        <SheetContent side="right" className={cn("w-full sm:w-[540px]", className)}>
          <SheetHeader>
            <SheetTitle>Event Details</SheetTitle>
          </SheetHeader>
          <div className="mt-6 h-[calc(100vh-120px)]">
            <EventContent />
          </div>
        </SheetContent>
      </Sheet>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className={cn("max-w-4xl max-h-[90vh]", className)}>
        <DialogHeader>
          <DialogTitle>Event Details</DialogTitle>
        </DialogHeader>
        <div className="h-[70vh]">
          <EventContent />
        </div>
      </DialogContent>
    </Dialog>
  );
}
